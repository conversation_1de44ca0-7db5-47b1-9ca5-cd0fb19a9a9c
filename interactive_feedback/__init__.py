"""
Interactive Feedback System for Blender AI Agent

This module provides an interactive feedback system that allows users to 
review and provide feedback at each stage of the 3D model generation process.

Inspired by mcp-feedback-enhanced, this system enables:
- Step-by-step user feedback and confirmation
- Dynamic workflow adjustment based on user input
- Intelligent auto-proceed mechanisms
- Multi-interface support (Web UI, Desktop, CLI)

Author: Augment Agent
Date: 2025-07-19
Version: 1.0.0
"""

from .core import (
    InteractiveFeedbackManager,
    FeedbackRequest,
    FeedbackResponse,
    FeedbackOption,
    FeedbackAction,
    FeedbackStage,
    AutoProceedConfig
)

from .orchestrator import (
    InteractiveOrchestratorAgent,
    InteractiveOrchestrationConfig,
    StageExecutor,
    FeedbackStage
)

from .interfaces import (
    WebFeedbackInterface,
    DesktopFeedbackInterface,
    CLIFeedbackInterface,
    FeedbackInterfaceFactory
)

from .analytics import (
    FeedbackAnalytics,
    UserPreferenceManager,
    AdaptiveStrategy,
    PerformanceMetrics
)

from .server import (
    BlenderAIFeedbackServer,
    WebSocketManager,
    FeedbackAPIRouter
)

__all__ = [
    # Core components
    'InteractiveFeedbackManager',
    'FeedbackRequest',
    'FeedbackResponse',
    'FeedbackOption',
    'FeedbackAction',
    'FeedbackStage',
    'AutoProceedConfig',
    
    # Orchestrator
    'InteractiveOrchestratorAgent',
    'InteractiveOrchestrationConfig',
    'StageExecutor',
    'FeedbackStage',
    
    # Interfaces
    'WebFeedbackInterface',
    'DesktopFeedbackInterface', 
    'CLIFeedbackInterface',
    'FeedbackInterfaceFactory',
    
    # Analytics
    'FeedbackAnalytics',
    'UserPreferenceManager',
    'AdaptiveStrategy',
    'PerformanceMetrics',
    
    # Server
    'BlenderAIFeedbackServer',
    'WebSocketManager',
    'FeedbackAPIRouter'
]

__version__ = "1.0.0"
