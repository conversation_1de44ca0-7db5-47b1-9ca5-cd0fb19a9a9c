"""
Server components for the Interactive Feedback System.

This module provides the web server infrastructure for handling
feedback requests through WebSocket and HTTP APIs.
"""

import asyncio
import json
import logging
import uuid
from typing import Dict, Any, Optional, Set
from datetime import datetime
from pathlib import Path

import uvicorn
from fastapi import FastAP<PERSON>, WebSocket, WebSocketDisconnect, HTTPException
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse
from fastapi.middleware.cors import CORSMiddleware

from .core import FeedbackRe<PERSON>, FeedbackResponse, InteractiveFeedbackManager

logger = logging.getLogger(__name__)


class WebSocketManager:
    """Manages WebSocket connections for real-time feedback communication."""
    
    def __init__(self):
        """Initialize WebSocket manager."""
        self.active_connections: Dict[str, WebSocket] = {}
        self.session_connections: Dict[str, Set[str]] = {}
        
    async def connect(self, websocket: WebSocket, session_id: str):
        """Accept a new WebSocket connection."""
        await websocket.accept()
        connection_id = str(uuid.uuid4())
        
        self.active_connections[connection_id] = websocket
        
        if session_id not in self.session_connections:
            self.session_connections[session_id] = set()
        self.session_connections[session_id].add(connection_id)
        
        logger.info(f"WebSocket connected: {connection_id} for session {session_id}")
        return connection_id
    
    def disconnect(self, connection_id: str, session_id: str):
        """Remove a WebSocket connection."""
        if connection_id in self.active_connections:
            del self.active_connections[connection_id]
        
        if session_id in self.session_connections:
            self.session_connections[session_id].discard(connection_id)
            if not self.session_connections[session_id]:
                del self.session_connections[session_id]
        
        logger.info(f"WebSocket disconnected: {connection_id}")
    
    async def send_personal_message(self, message: Dict[str, Any], connection_id: str):
        """Send a message to a specific connection."""
        if connection_id in self.active_connections:
            websocket = self.active_connections[connection_id]
            try:
                await websocket.send_text(json.dumps(message))
            except Exception as e:
                logger.error(f"Failed to send message to {connection_id}: {e}")
                # Remove dead connection
                for session_id, connections in self.session_connections.items():
                    if connection_id in connections:
                        self.disconnect(connection_id, session_id)
                        break
    
    async def broadcast_to_session(self, session_id: str, message: Dict[str, Any]):
        """Broadcast a message to all connections in a session."""
        if session_id in self.session_connections:
            connections = list(self.session_connections[session_id])
            for connection_id in connections:
                await self.send_personal_message(message, connection_id)


class FeedbackAPIRouter:
    """API router for feedback-related endpoints."""
    
    def __init__(self, feedback_manager: InteractiveFeedbackManager):
        """Initialize API router."""
        self.feedback_manager = feedback_manager
    
    def setup_routes(self, app: FastAPI):
        """Set up API routes."""
        
        @app.get("/api/session/{session_id}/status")
        async def get_session_status(session_id: str):
            """Get current session status."""
            try:
                # Check if session exists
                if session_id != self.feedback_manager.session_id:
                    raise HTTPException(status_code=404, detail="Session not found")
                
                return {
                    "session_id": session_id,
                    "status": "active",
                    "current_stage": getattr(self.feedback_manager.current_session, 'current_stage', None),
                    "timestamp": datetime.now().isoformat()
                }
            except Exception as e:
                logger.error(f"Error getting session status: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        @app.post("/api/session/{session_id}/feedback")
        async def submit_feedback(session_id: str, feedback_data: Dict[str, Any]):
            """Submit feedback response."""
            try:
                # Validate session
                if session_id != self.feedback_manager.session_id:
                    raise HTTPException(status_code=404, detail="Session not found")
                
                # Process feedback
                request_id = feedback_data.get("request_id")
                if not request_id:
                    raise HTTPException(status_code=400, detail="Missing request_id")
                
                # Submit feedback to manager
                await self.feedback_manager.submit_feedback_response(
                    request_id=request_id,
                    response_data=feedback_data
                )
                
                return {"status": "success", "message": "Feedback submitted"}
                
            except Exception as e:
                logger.error(f"Error submitting feedback: {e}")
                raise HTTPException(status_code=500, detail=str(e))


class BlenderAIFeedbackServer:
    """Main server for the Blender AI Feedback System."""
    
    def __init__(self, 
                 host: str = "127.0.0.1", 
                 port: int = 8765,
                 feedback_manager: Optional[InteractiveFeedbackManager] = None):
        """
        Initialize the feedback server.
        
        Args:
            host: Server host address
            port: Server port
            feedback_manager: Feedback manager instance
        """
        self.host = host
        self.port = port
        self.feedback_manager = feedback_manager
        
        # Initialize FastAPI app
        self.app = FastAPI(
            title="Blender AI Feedback Server",
            description="Interactive feedback system for Blender AI Agent",
            version="1.0.0"
        )
        
        # Initialize managers
        self.websocket_manager = WebSocketManager()
        self.api_router = FeedbackAPIRouter(feedback_manager) if feedback_manager else None
        
        # Server state
        self.server_task = None
        self.is_running = False
        
        self._setup_middleware()
        self._setup_routes()
    
    def _setup_middleware(self):
        """Set up FastAPI middleware."""
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
    
    def _setup_routes(self):
        """Set up server routes."""
        
        @self.app.get("/")
        async def root():
            """Serve the main feedback interface."""
            return HTMLResponse(self._get_feedback_html())
        
        @self.app.websocket("/ws/{session_id}")
        async def websocket_endpoint(websocket: WebSocket, session_id: str):
            """WebSocket endpoint for real-time communication."""
            connection_id = await self.websocket_manager.connect(websocket, session_id)
            
            try:
                while True:
                    # Wait for messages from client
                    data = await websocket.receive_text()
                    message = json.loads(data)
                    
                    # Handle different message types
                    await self._handle_websocket_message(message, session_id, connection_id)
                    
            except WebSocketDisconnect:
                self.websocket_manager.disconnect(connection_id, session_id)
            except Exception as e:
                logger.error(f"WebSocket error: {e}")
                self.websocket_manager.disconnect(connection_id, session_id)
        
        # Set up API routes if feedback manager is available
        if self.api_router:
            self.api_router.setup_routes(self.app)
    
    async def _handle_websocket_message(self, message: Dict[str, Any], session_id: str, connection_id: str):
        """Handle incoming WebSocket messages."""
        message_type = message.get("type")
        
        if message_type == "feedback_response":
            # Handle feedback response from client
            if self.feedback_manager:
                await self.feedback_manager.submit_feedback_response(
                    request_id=message.get("request_id"),
                    response_data=message.get("data", {})
                )
        
        elif message_type == "ping":
            # Respond to ping with pong
            await self.websocket_manager.send_personal_message(
                {"type": "pong", "timestamp": datetime.now().isoformat()},
                connection_id
            )
        
        else:
            logger.warning(f"Unknown message type: {message_type}")
    
    def _get_feedback_html(self) -> str:
        """Generate the feedback interface HTML."""
        return """
        <!DOCTYPE html>
        <html>
        <head>
            <title>Blender AI Feedback Interface</title>
            <meta charset="utf-8">
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .container { max-width: 800px; margin: 0 auto; }
                .stage { background: #f5f5f5; padding: 20px; margin: 10px 0; border-radius: 5px; }
                .options { margin: 15px 0; }
                .option { margin: 5px 0; }
                button { padding: 10px 20px; margin: 5px; cursor: pointer; }
                .auto-proceed { background: #e8f4fd; border: 1px solid #bee5eb; padding: 10px; margin: 10px 0; }
                .status { position: fixed; top: 10px; right: 10px; padding: 10px; background: #d4edda; border-radius: 5px; }
            </style>
        </head>
        <body>
            <div class="container">
                <h1>🎯 Blender AI 交互式反馈系统</h1>
                <div class="status" id="status">连接中...</div>
                <div id="content">
                    <p>等待反馈请求...</p>
                </div>
            </div>
            
            <script>
                const urlParams = new URLSearchParams(window.location.search);
                const sessionId = urlParams.get('session') || 'default';
                const ws = new WebSocket(`ws://localhost:8765/ws/${sessionId}`);
                
                ws.onopen = function() {
                    document.getElementById('status').textContent = '已连接';
                    document.getElementById('status').style.background = '#d4edda';
                };
                
                ws.onclose = function() {
                    document.getElementById('status').textContent = '连接断开';
                    document.getElementById('status').style.background = '#f8d7da';
                };
                
                ws.onmessage = function(event) {
                    const data = JSON.parse(event.data);
                    if (data.type === 'feedback_request') {
                        displayFeedbackRequest(data.request);
                    }
                };
                
                function displayFeedbackRequest(request) {
                    const content = document.getElementById('content');
                    content.innerHTML = `
                        <div class="stage">
                            <h2>${request.title}</h2>
                            <p><strong>阶段:</strong> ${request.stage}</p>
                            <div class="content">${JSON.stringify(request.content, null, 2)}</div>
                            <div class="options">
                                ${request.options.map(option => `
                                    <div class="option">
                                        <button onclick="submitFeedback('${request.request_id}', '${option.action}')">
                                            ${option.label}
                                        </button>
                                    </div>
                                `).join('')}
                            </div>
                            ${request.auto_proceed ? `
                                <div class="auto-proceed">
                                    <p>⏱️ 自动继续: ${request.auto_proceed.timeout_seconds}秒后自动选择默认选项</p>
                                </div>
                            ` : ''}
                        </div>
                    `;
                }
                
                function submitFeedback(requestId, action) {
                    ws.send(JSON.stringify({
                        type: 'feedback_response',
                        request_id: requestId,
                        data: { action: action }
                    }));
                }
            </script>
        </body>
        </html>
        """
    
    async def start(self):
        """Start the feedback server."""
        if self.is_running:
            logger.warning("Server is already running")
            return
        
        try:
            config = uvicorn.Config(
                app=self.app,
                host=self.host,
                port=self.port,
                log_level="info"
            )
            server = uvicorn.Server(config)
            
            # Start server in background
            self.server_task = asyncio.create_task(server.serve())
            self.is_running = True
            
            logger.info(f"Feedback server started at http://{self.host}:{self.port}")
            
        except Exception as e:
            logger.error(f"Failed to start server: {e}")
            raise
    
    async def stop(self):
        """Stop the feedback server."""
        if not self.is_running:
            return
        
        if self.server_task:
            self.server_task.cancel()
            try:
                await self.server_task
            except asyncio.CancelledError:
                pass
        
        self.is_running = False
        logger.info("Feedback server stopped")
    
    async def broadcast_to_session(self, session_id: str, message: Dict[str, Any]):
        """Broadcast message to all connections in a session."""
        await self.websocket_manager.broadcast_to_session(session_id, message)
