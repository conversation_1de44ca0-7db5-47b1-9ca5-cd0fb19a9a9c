# Blender 3D模型生成AI Agent系统 - 用户手册

## 目录

1. [系统概述](#系统概述)
2. [快速开始](#快速开始)
3. [交互式反馈系统](#交互式反馈系统)
4. [多LLM提供商支持](#多llm提供商支持)
5. [功能详解](#功能详解)
6. [API接口说明](#api接口说明)
7. [常见问题](#常见问题)
8. [故障排除](#故障排除)

## 系统概述

Blender 3D模型生成AI Agent系统是一个基于人工智能的端到端3D建模解决方案，能够从图像输入自动生成高质量的Blender 3D模型。系统采用**交互式反馈机制**，让用户在每个步骤都能参与和控制生成过程。

### 🆕 最新特性

- **🔄 交互式反馈**: 每个步骤都可以获得用户反馈和确认
- **🧠 多LLM支持**: 支持OpenAI、Gemini、DeepSeek、Kimi等多种AI模型
- **💰 成本优化**: 智能选择最经济的AI模型，节省60-80%成本
- **🎯 自适应学习**: 系统学习用户偏好，提供个性化体验
- **🌐 多界面支持**: Web UI、桌面应用、命令行三种交互方式

### 核心功能

- **图像分析**: 智能识别图像中的3D几何体、材质、颜色等特征
- **规格生成**: 自动生成符合标准的3D模型规格文件
- **代码生成**: 将规格转换为可执行的Blender Python脚本
- **模型生成**: 在Blender中执行脚本，生成最终的3D模型
- **质量控制**: 自动验证和调试，确保模型质量
- **视觉反馈**: 智能比较生成结果与原始图像，提供改进建议
- **🆕 交互式控制**: 用户可在任何步骤进行干预和调整

### 支持的输入格式

- **图像格式**: PNG, JPG, BMP
- **图像来源**: 本地文件、URL链接、AI生成图像（DALL-E集成）
- **分辨率**: 建议256x256像素以上

### 输出格式

- **3D模型**: .blend文件（Blender原生格式）
- **渲染图**: PNG格式的预览图
- **规格文件**: JSON格式的模型规格
- **代码文件**: Python脚本文件

## 快速开始

### 环境要求

- Python 3.11+
- Blender 4.0+
- 8GB+ RAM
- GPU推荐（用于图像分析和渲染）

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd models
```

2. **创建环境**
```bash
conda env create -f environment.yml
conda activate bl4.4env
```

3. **安装依赖**
```bash
pip install -r requirements.txt
```

4. **配置环境变量**
```bash
cp env_example .env
# 编辑.env文件，设置BLENDER_PATH和AI模型API密钥
```

关键配置项：
```bash
# Blender路径
BLENDER_PATH=/usr/bin/blender

# 主要LLM提供商（推荐使用成本较低的模型）
PRIMARY_LLM_PROVIDER=gemini
GEMINI_API_KEY=your_gemini_api_key_here
GEMINI_MODEL=gemini-1.5-flash

# 备用提供商
OPENAI_API_KEY=your_openai_api_key_here
DEEPSEEK_API_KEY=your_deepseek_api_key_here
KIMI_API_KEY=your_kimi_api_key_here

# 交互式反馈配置
FEEDBACK_INTERFACE=web  # web, desktop, cli
ENABLE_AUTO_PROCEED=true
AUTO_PROCEED_TIMEOUT=30
```

5. **验证安装**
```bash
# 验证基础功能
python -m pytest tests/test_blender_executor.py -v

# 验证多LLM支持
python scripts/verify_multi_llm.py

# 验证交互式反馈系统
python scripts/verify_interactive_feedback.py
```

### 第一个模型

#### 方式1: 交互式生成（推荐）
1. **准备图像**
   - 将图像文件放在`demo_images/`目录下
   - 确保图像清晰，包含明确的几何形状

2. **启动交互式生成**
```bash
# Web界面（推荐新用户）
python examples/interactive_feedback_demo.py --interface web

# 命令行界面（适合开发者）
python examples/interactive_feedback_demo.py --interface cli

# 桌面应用（需要额外安装）
python examples/interactive_feedback_demo.py --interface desktop
```

3. **跟随界面指引**
   - 系统会在每个步骤展示结果
   - 您可以选择继续、重试或手动调整
   - 系统会学习您的偏好，逐渐减少干预需求

#### 方式2: 自动化生成
```bash
# 传统的端到端自动生成
python demo_orchestrator_end_to_end.py

# 使用多LLM优化的自动生成
python examples/multi_llm_demo.py
```

#### 查看结果
- 生成的模型: `output/models/`
- 渲染图: `output/renders/`
- 规格文件: `output/specs/`
- 生成代码: `output/code/`
- 日志文件: `logs/`
- 反馈历史: `user_data/`

## 交互式反馈系统

### 系统概述

交互式反馈系统是本系统的核心创新功能，灵感来源于mcp-feedback-enhanced项目。它让用户在3D模型生成的每个关键步骤都能参与决策，确保最终结果符合用户期望。

### 工作流程

```mermaid
graph TD
    A[图像输入] --> B[图像预处理]
    B --> C[用户确认: 图像质量]
    C -->|满意| D[图像分析]
    C -->|重新处理| B
    D --> E[用户确认: 分析结果]
    E -->|满意| F[规格生成]
    E -->|调整| D
    F --> G[用户确认: 模型规格]
    G -->|满意| H[代码生成]
    G -->|修改| F
    H --> I[用户确认: 生成代码]
    I -->|满意| J[模型生成]
    I -->|优化| H
    J --> K[用户确认: 最终模型]
    K -->|满意| L[完成]
    K -->|改进| M[视觉反馈优化]
    M --> F
```

### 反馈点详解

#### 1. 图像预处理确认
- **展示内容**: 原始图像 vs 处理后图像
- **可选操作**:
  - ✅ 确认继续
  - 🔄 调整处理参数
  - 🖼️ 更换图像

#### 2. 图像分析确认
- **展示内容**: 识别的对象、置信度、场景属性
- **可选操作**:
  - ✅ 分析正确
  - ✏️ 手动调整识别结果
  - 🔄 重新分析（调整粒度）

#### 3. 规格生成确认
- **展示内容**: JSON规格文件、预览渲染图
- **可选操作**:
  - ✅ 规格满意
  - ✏️ 交互式编辑规格
  - 🔄 重新生成（调整风格/复杂度）

#### 4. 代码生成确认
- **展示内容**: Blender Python代码、语法检查结果
- **可选操作**:
  - ✅ 代码满意
  - ✏️ 手动编辑代码
  - 🔄 重新生成（调整优化级别）

#### 5. 模型生成确认
- **展示内容**: 3D模型文件、多角度渲染图
- **可选操作**:
  - ✅ 模型满意
  - 🎨 视觉优化建议
  - 🔄 重新生成

### 界面类型

#### Web界面（推荐）
- **优势**: 直观易用、实时预览、跨平台
- **适用**: 新用户、设计师、一般用户
- **启动**: `python examples/interactive_feedback_demo.py --interface web`

#### 桌面应用
- **优势**: 原生性能、离线使用、深度集成
- **适用**: 专业用户、频繁使用者
- **启动**: `python examples/interactive_feedback_demo.py --interface desktop`

#### 命令行界面
- **优势**: 轻量快速、脚本友好、远程使用
- **适用**: 开发者、自动化场景
- **启动**: `python examples/interactive_feedback_demo.py --interface cli`

### 智能特性

#### 自动继续机制
系统会根据以下因素决定是否自动继续：
- **结果置信度**: 高置信度结果自动继续
- **用户历史**: 学习用户在类似情况下的选择
- **成功率**: 该步骤的历史成功率
- **用户设置**: 个人偏好配置

#### 用户偏好学习
- **响应时间模式**: 学习用户的决策速度
- **操作偏好**: 记录用户常用的操作选择
- **质量标准**: 适应用户的质量要求
- **风格偏好**: 学习用户喜欢的艺术风格

## 多LLM提供商支持

### 支持的提供商

| 提供商 | 推荐模型 | 特点 | 相对成本 | 适用场景 |
|--------|----------|------|----------|----------|
| **OpenAI** | gpt-4o-mini | 高质量、多模态 | 中等 | 复杂分析、多模态任务 |
| **Google Gemini** | gemini-1.5-flash | 长上下文、快速 | 极低 | 日常使用、成本敏感 |
| **DeepSeek** | deepseek-chat | 代码优化 | 极低 | 代码生成、技术任务 |
| **Kimi** | moonshot-v1-8k | 中文优化 | 低 | 中文用户、长文本 |

### 智能选择策略

#### 成本优先模式（推荐）
```bash
# 环境变量配置
LLM_LOAD_BALANCING=least_cost
PRIMARY_LLM_PROVIDER=gemini
```
- 优先使用成本最低的模型
- 预计节省60-80%的AI使用成本
- 适合大多数日常使用场景

#### 质量优先模式
```bash
LLM_LOAD_BALANCING=least_latency
PRIMARY_LLM_PROVIDER=openai
```
- 优先使用质量最高的模型
- 适合专业用户和重要项目
- 成本较高但结果更可靠

#### 平衡模式
```bash
LLM_LOAD_BALANCING=weighted
```
- 根据任务类型智能选择
- 平衡成本和质量
- 适合混合使用场景

### 自动回退机制

当主要提供商不可用时，系统会自动切换：
1. **健康检查**: 定期检测各提供商状态
2. **智能回退**: 按预设优先级自动切换
3. **负载均衡**: 分散请求到多个提供商
4. **成本控制**: 避免超出预算限制

### 配置示例

#### 基础配置
```bash
# 主要提供商
PRIMARY_LLM_PROVIDER=gemini
GEMINI_API_KEY=your_key_here

# 启用回退
ENABLE_LLM_FALLBACK=true

# 成本控制
LLM_COST_THRESHOLD=0.10  # 每次请求最大成本$0.10
```

#### 高级配置
```bash
# 负载均衡策略
LLM_LOAD_BALANCING=least_cost

# 提供商权重（数字越大优先级越高）
OPENAI_WEIGHT=1.0
GEMINI_WEIGHT=2.0
DEEPSEEK_WEIGHT=1.5
KIMI_WEIGHT=1.0

# 性能阈值
LLM_LATENCY_THRESHOLD=30.0  # 最大延迟30秒
ENABLE_LLM_HEALTH_CHECKS=true
```

## 功能详解

### 图像分析

系统支持多种分析粒度：

- **BASIC**: 基础形状识别（立方体、球体、圆柱体等）
- **DETAILED**: 详细特征分析（材质、纹理、相对位置）
- **COMPREHENSIVE**: 全面场景理解（深度、遮挡、复杂结构）

```python
from agents.image_analysis_agent import ImageAnalysisAgent, AnalysisGranularity

agent = ImageAnalysisAgent()
result = agent.analyze_image(
    image_path="path/to/image.png",
    granularity=AnalysisGranularity.DETAILED
)
```

### 规格生成

支持两个版本的规格Schema：

- **v1.0.0**: 基础几何体和材质
- **v2.0.0**: 复杂材质、修改器、动画、MCP结构

```python
from agents.spec_generation_agent import SpecGenerationAgent, SpecGenerationConfig

config = SpecGenerationConfig(
    schema_version="v2.0.0",
    enable_complex_materials=True,
    enable_modifiers=True
)
agent = SpecGenerationAgent(config=config)
```

### 代码生成

支持多种Blender功能：

- **基础几何体**: 立方体、球体、圆柱体、平面、圆锥
- **复杂材质**: PBR、玻璃、发光、次表面散射等
- **修改器**: 阵列、镜像、实体化、倒角等
- **MCP结构**: 分子模型、蛋白质结构

### 质量控制

- **语法检查**: AST静态分析
- **执行验证**: Blender环境测试
- **错误诊断**: 智能错误分类和修复建议
- **性能优化**: 代码效率分析

## API接口说明

### 主要API端点

#### 1. 图像上传
```http
POST /upload_image
Content-Type: multipart/form-data

{
  "image": <file>,
  "metadata": {
    "name": "my_image",
    "description": "A red cube"
  }
}
```

#### 2. 开始交互式建模任务
```http
POST /start_interactive_modeling
Content-Type: application/json

{
  "image_id": "uuid-1234",
  "preferences": {
    "units": "meters",
    "quality": "high",
    "style": "realistic",
    "feedback_interface": "web",
    "enable_auto_proceed": true,
    "llm_provider": "gemini"
  },
  "model_name": "MyModel",
  "description": "Generated from uploaded image"
}
```

#### 2b. 开始自动化建模任务
```http
POST /start_modeling
Content-Type: application/json

{
  "image_id": "uuid-1234",
  "preferences": {
    "units": "meters",
    "quality": "high",
    "style": "realistic",
    "llm_provider": "auto"  # 自动选择最优提供商
  },
  "model_name": "MyModel",
  "description": "Generated from uploaded image"
}
```

#### 3. 查询任务状态
```http
GET /task_status/{task_id}

Response:
{
  "task_id": "uuid-5678",
  "status": "processing",
  "progress": 65,
  "current_stage": "code_generation",
  "estimated_completion": "2025-07-19T15:30:00Z",
  "feedback_required": false,
  "llm_provider_used": "gemini",
  "cost_so_far": 0.05,
  "feedback_history": [
    {
      "stage": "image_analysis",
      "action": "proceed",
      "timestamp": "2025-07-19T15:25:00Z"
    }
  ]
}
```

#### 3b. 提交用户反馈
```http
POST /submit_feedback/{task_id}
Content-Type: application/json

{
  "stage": "specification_generation",
  "action": "manual_adjust",
  "parameters": {
    "object_count": 3,
    "material_type": "metallic"
  },
  "user_input": "请增加一个金属材质的球体"
}
```

#### 4. 下载模型
```http
GET /download_model/{model_id}

Response: Binary .blend file
```

### Python SDK

#### 交互式建模（推荐）
```python
from interactive_feedback import InteractiveOrchestratorAgent, InteractiveOrchestrationConfig

# 初始化交互式编排器
config = InteractiveOrchestrationConfig(
    feedback_interface_type="web",  # web, desktop, cli
    enable_auto_proceed=True,
    auto_proceed_confidence_threshold=0.8,
    enable_adaptive_learning=True,
    primary_llm_provider="gemini"  # 成本优化
)

orchestrator = InteractiveOrchestratorAgent(config=config)

# 执行交互式建模任务
result = await orchestrator.orchestrate_task_interactive(
    image_path="path/to/image.png",
    user_preferences={
        "units": "meters",
        "style": "realistic",
        "quality": "high"
    },
    model_name="MyModel",
    description="Interactive test model"
)

print(f"Model generated: {result.model_path}")
print(f"Render saved: {result.render_path}")
print(f"Feedback sessions: {len(result.feedback_history['requests'])}")
print(f"Total cost: ${result.feedback_history.get('total_cost', 0):.4f}")
```

#### 多LLM提供商使用
```python
from llm_providers import LLMManager, LLMManagerConfig, create_llm_provider

# 创建LLM管理器
config = LLMManagerConfig(
    fallback_strategy="cheapest_first",
    load_balancing_strategy="least_cost",
    cost_threshold=0.10
)
manager = LLMManager(config)

# 添加多个提供商
providers = [
    ("gemini", "gemini-1.5-flash"),
    ("deepseek", "deepseek-chat"),
    ("openai", "gpt-4o-mini")
]

for name, model in providers:
    try:
        provider = create_llm_provider(name, model)
        manager.add_provider(name, provider)
    except Exception as e:
        print(f"Failed to add {name}: {e}")

# 使用管理器进行LLM调用
from llm_providers.base import LLMMessage, MessageRole

messages = [
    LLMMessage(role=MessageRole.USER, content="Generate a cube specification")
]

response = manager.chat_completion(messages)
print(f"Response from {response.provider.value}: {response.content}")
print(f"Cost: ${response.usage.cost_usd:.6f}")
```

#### 传统自动化建模
```python
from main_orchestrator import OrchestratorAgent, OrchestrationConfig

# 初始化传统编排器
config = OrchestrationConfig(
    enable_inner_loop=True,
    enable_outer_loop=True,
    max_iterations=3
)
orchestrator = OrchestratorAgent(config=config)

# 执行自动化建模任务
result = orchestrator.orchestrate_task(
    image_path="path/to/image.png",
    user_preferences={"units": "meters"},
    model_name="MyModel",
    description="Automated test model"
)

print(f"Model generated: {result.model_path}")
print(f"Render saved: {result.render_path}")
```

## 常见问题

### 🆕 交互式反馈相关

#### Q: 什么是交互式反馈系统？
A: 交互式反馈系统让您在3D模型生成的每个关键步骤都能查看结果并提供反馈。您可以选择继续、重试或手动调整，确保最终结果符合期望。

#### Q: 哪种界面最适合我？
A:
- **Web界面**: 适合新用户，直观易用，支持实时预览
- **桌面应用**: 适合专业用户，性能更好，支持离线使用
- **命令行**: 适合开发者，轻量快速，便于自动化

#### Q: 自动继续功能如何工作？
A: 系统会根据结果置信度、您的历史偏好和成功率来决定是否自动继续。您可以随时暂停自动继续并手动干预。

#### Q: 系统如何学习我的偏好？
A: 系统会记录您在各个步骤的选择、响应时间和满意度，逐渐学习您的偏好模式，减少不必要的干预。

### 💰 多LLM提供商相关

#### Q: 使用多LLM提供商能节省多少成本？
A: 根据使用模式，通常可以节省60-80%的AI使用成本。例如，使用Gemini Flash替代GPT-4可以节省99.5%的成本。

#### Q: 推荐使用哪个LLM提供商？
A:
- **日常使用**: Gemini Flash（成本极低，速度快）
- **代码生成**: DeepSeek（专门优化，成本低）
- **复杂分析**: OpenAI GPT-4o（质量高，支持多模态）
- **中文用户**: Kimi（中文优化，长上下文）

#### Q: 如何配置自动回退？
A: 在`.env`文件中设置：
```bash
ENABLE_LLM_FALLBACK=true
LLM_LOAD_BALANCING=least_cost
```
系统会自动在提供商之间切换以确保服务可用性。

### 🔧 技术问题

#### Q: 支持哪些类型的图像？
A: 系统支持包含清晰几何形状的图像，如建筑物、产品设计、简单场景等。抽象艺术或复杂自然场景可能效果不佳。

#### Q: 生成的模型质量如何？
A: 模型质量取决于输入图像的清晰度和复杂度。简单几何体通常能达到90%+的准确率，复杂场景可能需要人工调整。交互式反馈系统可以显著提高最终质量。

#### Q: 可以自定义材质吗？
A: 是的，系统支持多种材质类型，包括PBR、玻璃、金属等。可以通过用户偏好设置、交互式编辑或直接修改生成的规格文件。

#### Q: 支持动画吗？
A: v2.0.0版本支持基础关键帧动画。复杂动画建议在Blender中手动调整。

#### Q: 如何提高生成速度？
A: 可以通过以下方式优化：
- 使用更快的LLM模型（如Gemini Flash）
- 启用自动继续功能，减少等待时间
- 使用GPU加速
- 降低图像分析粒度
- 禁用不必要的功能（如外循环反馈）

## 故障排除

### 🆕 交互式反馈问题

#### 1. Web界面无法打开
**症状**: 浏览器显示连接错误
**解决方案**:
```bash
# 检查端口是否被占用
netstat -an | grep 8765

# 尝试不同端口
python examples/interactive_feedback_demo.py --interface web --port 8766

# 检查防火墙设置
sudo ufw allow 8765
```

#### 2. 反馈界面卡住不响应
**症状**: 界面显示但无法交互
**解决方案**:
- 刷新浏览器页面
- 检查WebSocket连接状态
- 重启反馈服务
- 切换到CLI界面作为备选

#### 3. 自动继续功能异常
**症状**: 系统不会自动继续或立即跳过
**解决方案**:
```bash
# 检查配置
grep AUTO_PROCEED .env

# 重置用户偏好
rm -rf user_data/

# 调整置信度阈值
AUTO_PROCEED_CONFIDENCE_THRESHOLD=0.7
```

### 💰 多LLM提供商问题

#### 1. API密钥错误
**症状**: `Invalid API key` 或认证失败
**解决方案**:
```bash
# 验证API密钥
python scripts/verify_multi_llm.py

# 检查环境变量
echo $OPENAI_API_KEY
echo $GEMINI_API_KEY

# 重新设置密钥
export GEMINI_API_KEY=your_new_key_here
```

#### 2. 提供商服务不可用
**症状**: 特定LLM提供商连接失败
**解决方案**:
- 检查网络连接
- 验证API配额和限制
- 启用自动回退机制
- 手动切换到其他提供商

#### 3. 成本超出预期
**症状**: LLM使用费用过高
**解决方案**:
```bash
# 设置成本限制
LLM_COST_THRESHOLD=0.05

# 切换到更便宜的提供商
PRIMARY_LLM_PROVIDER=gemini

# 启用成本优化
LLM_LOAD_BALANCING=least_cost
```

### 🔧 传统问题

#### 1. Blender执行失败
**症状**: `BlenderExecutor`报错，无法生成模型
**解决方案**:
- 检查Blender路径配置
- 确认Blender版本兼容性（推荐4.0+）
- 查看详细错误日志
- 在交互式模式下检查生成的代码

#### 2. 图像分析失败
**症状**: 无法识别图像中的对象
**解决方案**:
- 确保图像清晰度足够
- 尝试不同的分析粒度
- 检查图像格式是否支持
- 使用交互式模式手动调整识别结果

#### 3. 内存不足
**症状**: 系统运行缓慢或崩溃
**解决方案**:
- 增加系统内存
- 使用更轻量的LLM模型
- 减少并发任务数量
- 降低图像分辨率
- 关闭其他占用内存的程序

#### 4. LLM调用失败
**症状**: AI模型API调用失败或响应异常
**解决方案**:
- 检查所有LLM提供商的API密钥配置
- 确认网络连接和防火墙设置
- 查看API使用限额和配额
- 启用自动回退机制
- 切换到备用提供商

### 🔍 日志分析

#### 系统日志位置
- `logs/orchestrator.log`: 主要工作流日志
- `logs/agents/`: 各Agent的详细日志
- `logs/blender_execution.log`: Blender执行日志
- `logs/errors.log`: 错误和异常日志
- `logs/feedback/`: 交互式反馈日志
- `logs/llm_providers/`: 多LLM提供商日志

#### 🆕 反馈系统日志
```bash
# 查看反馈会话日志
tail -f logs/feedback/session_*.log

# 分析用户偏好变化
grep "preference_update" logs/feedback/*.log

# 查看自动继续决策
grep "auto_proceed" logs/feedback/*.log
```

#### 🆕 LLM提供商日志
```bash
# 查看提供商切换记录
grep "provider_switch" logs/llm_providers/*.log

# 分析成本使用情况
grep "cost_tracking" logs/llm_providers/*.log

# 查看健康检查状态
grep "health_check" logs/llm_providers/*.log
```

### 📊 性能监控

#### 传统性能监控
```python
from tests.test_performance_monitoring import PerformanceMonitor

monitor = PerformanceMonitor()
with monitor.start_monitoring("ImageAnalysis", "analyze_complex_scene"):
    # 执行图像分析
    result = agent.analyze_image(image_path)

# 查看性能报告
summary = monitor.get_metrics_summary()
print(f"平均执行时间: {summary['average_time']:.2f}s")
print(f"内存使用: {summary['average_memory_usage_mb']:.1f}MB")
```

#### 🆕 交互式反馈监控
```python
from interactive_feedback.analytics import FeedbackAnalytics

analytics = FeedbackAnalytics()

# 获取用户满意度分析
overall_analytics = analytics.get_overall_analytics()
print(f"用户满意度: {overall_analytics['avg_user_satisfaction']:.2f}")
print(f"平均完成时间: {overall_analytics['avg_completion_time']:.1f}秒")

# 获取改进建议
insights = analytics.generate_insights()
for insight in insights:
    print(f"💡 {insight}")
```

#### 🆕 LLM成本监控
```python
from llm_providers import LLMManager

manager = LLMManager()

# 获取成本统计
metrics = manager.get_metrics_summary()
for provider, stats in metrics.items():
    print(f"{provider}:")
    print(f"  - 总成本: ${stats['total_cost']:.4f}")
    print(f"  - 平均成本: ${stats['average_cost']:.6f}")
    print(f"  - 成功率: {stats['success_rate']:.1%}")

# 获取最优提供商建议
best_cost = manager.get_best_provider("cost")
best_speed = manager.get_best_provider("latency")
print(f"💰 成本最优: {best_cost}")
print(f"⚡ 速度最优: {best_speed}")
```

## 🚀 最佳实践

### 交互式使用建议

#### 新用户入门
1. **从Web界面开始**: 最直观易用，支持实时预览
2. **启用自动继续**: 减少不必要的等待时间
3. **使用成本优化模式**: 选择Gemini或DeepSeek降低成本
4. **从简单图像开始**: 清晰的几何形状更容易成功

#### 专业用户优化
1. **使用桌面应用**: 更好的性能和离线支持
2. **自定义偏好设置**: 根据工作流程调整参数
3. **批量处理**: 利用学习到的偏好处理相似任务
4. **集成到现有工具链**: 使用API和Python SDK

### LLM提供商选择策略

#### 按使用场景选择
- **日常实验**: Gemini Flash（成本极低）
- **专业项目**: OpenAI GPT-4o（质量最高）
- **代码重点**: DeepSeek（代码优化）
- **中文用户**: Kimi（中文优化）

#### 成本控制策略
```bash
# 设置每日成本限制
DAILY_COST_LIMIT=5.00

# 使用成本优先策略
LLM_LOAD_BALANCING=least_cost

# 设置单次请求限制
LLM_COST_THRESHOLD=0.10
```

### 质量优化技巧

#### 图像准备
- 使用高分辨率、清晰的图像
- 确保主要对象占据图像的主要部分
- 避免复杂背景和遮挡
- 良好的光照条件

#### 交互式调整
- 在图像分析阶段仔细检查识别结果
- 在规格生成阶段预览3D效果
- 在代码生成阶段检查语法和逻辑
- 充分利用手动调整功能

## 🔧 高级配置

### 环境变量完整列表

```bash
# === 基础配置 ===
BLENDER_PATH=/usr/bin/blender
OUTPUT_DIR=./output
LOGS_DIR=./logs

# === LLM提供商配置 ===
PRIMARY_LLM_PROVIDER=gemini
OPENAI_API_KEY=your_openai_key
GEMINI_API_KEY=your_gemini_key
DEEPSEEK_API_KEY=your_deepseek_key
KIMI_API_KEY=your_kimi_key

# === 交互式反馈配置 ===
FEEDBACK_INTERFACE=web
ENABLE_AUTO_PROCEED=true
AUTO_PROCEED_TIMEOUT=30
AUTO_PROCEED_CONFIDENCE_THRESHOLD=0.8

# === 成本控制 ===
LLM_COST_THRESHOLD=0.10
DAILY_COST_LIMIT=10.00
LLM_LOAD_BALANCING=least_cost

# === 性能优化 ===
MAX_CONCURRENT_TASKS=5
ENABLE_GPU_ACCELERATION=true
CACHE_ENABLED=true

# === 监控和调试 ===
DEBUG_MODE=false
ENABLE_MONITORING=true
METRICS_PORT=8080
LOG_LEVEL=INFO
```

### 自定义配置文件

创建 `config/custom.yaml`:
```yaml
orchestration:
  enable_inner_loop: true
  enable_outer_loop: true
  max_iterations: 3

feedback:
  interface_type: "web"
  auto_proceed:
    enabled: true
    timeout_seconds: 30
    confidence_threshold: 0.8

llm_providers:
  primary: "gemini"
  fallback_enabled: true
  cost_optimization: true

quality:
  image_analysis_granularity: "COMPREHENSIVE"
  code_generation_safety: true
  validation_strict: true
```

## 📞 技术支持

### 自助诊断

运行诊断脚本：
```bash
# 系统整体检查
python scripts/system_health_check.py

# 交互式反馈系统检查
python scripts/verify_interactive_feedback.py

# 多LLM支持检查
python scripts/verify_multi_llm.py

# 性能基准测试
python scripts/performance_benchmark.py
```

### 问题报告

如需技术支持，请提供以下信息：

#### 基础信息
1. 系统环境（Python版本、Blender版本、操作系统）
2. 使用的界面类型（Web/Desktop/CLI）
3. 配置的LLM提供商
4. 问题发生的具体步骤

#### 日志文件
1. 主要错误日志：`logs/errors.log`
2. 相关组件日志：`logs/agents/`
3. 反馈会话日志：`logs/feedback/`
4. LLM提供商日志：`logs/llm_providers/`

#### 复现信息
1. 输入图像（如果可能）
2. 用户偏好设置
3. 详细的复现步骤
4. 期望的结果 vs 实际结果

### 社区支持

- **GitHub Issues**: 报告Bug和功能请求
- **讨论区**: 使用经验分享和问题讨论
- **文档Wiki**: 社区维护的使用技巧
- **示例库**: 成功案例和最佳实践

---

## 📚 相关文档

- [部署指南](deployment_guide.md) - 详细的部署和配置说明
- [监控指南](monitoring_guide.md) - 系统监控和性能优化
- [多LLM迁移指南](multi_llm_migration_guide.md) - 从单一提供商迁移
- [交互式反馈提案](interactive_feedback_enhancement_proposal.md) - 技术细节
- [项目增强提案](project_enhancement_proposal.md) - 完整的改进方案

---

*本手册持续更新中，最新版本请参考项目文档。版本：v2.1.0 - 2025-07-19*
